import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'hoursToHMS',
  standalone: true
})
export class HoursToHMSPipe implements PipeTransform {
  transform(hours: number | null | undefined): string {
    if (hours === null || hours === undefined) {
      return 'N/A';
    }

    // Convert hours to total seconds for easier calculation
    const totalSeconds = Math.floor(hours * 3600);

    // Calculate years, days, hours, minutes, seconds
    const years = Math.floor(totalSeconds / (365 * 24 * 3600));
    const remainingAfterYears = totalSeconds % (365 * 24 * 3600);

    const days = Math.floor(remainingAfterYears / (24 * 3600));
    const remainingAfterDays = remainingAfterYears % (24 * 3600);

    const h = Math.floor(remainingAfterDays / 3600);
    const remainingAfterHours = remainingAfterDays % 3600;

    const m = Math.floor(remainingAfterHours / 60);
    const s = remainingAfterHours % 60;

    // Build display string with appropriate units
    const yearDisplay = years > 0 ? years + 'y ' : '';
    const dayDisplay = days > 0 ? days + 'd ' : '';
    const hDisplay = h > 0 ? h + 'h ' : '';
    const mDisplay = m > 0 ? m + 'm ' : '';
    const sDisplay = s > 0 ? s + 's' : '';

    const result = yearDisplay + dayDisplay + hDisplay + mDisplay + sDisplay;
    return result.trim() || '0s';
  }
}
