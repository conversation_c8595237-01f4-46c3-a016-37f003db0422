import { HoursToHMSPipe } from './hours-to-hms.pipe';

describe('HoursToHMSPipe', () => {
  let pipe: HoursToHMSPipe;

  beforeEach(() => {
    pipe = new HoursToHMSPipe();
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return "N/A" for null input', () => {
    expect(pipe.transform(null)).toBe('N/A');
  });

  it('should return "N/A" for undefined input', () => {
    expect(pipe.transform(undefined)).toBe('N/A');
  });

  it('should return "0s" for 0 hours', () => {
    expect(pipe.transform(0)).toBe('0s');
  });

  it('should handle seconds only', () => {
    expect(pipe.transform(0.001)).toBe('3s'); // 0.001 hours = 3.6 seconds ≈ 3s
  });

  it('should handle minutes and seconds', () => {
    expect(pipe.transform(0.5)).toBe('30m'); // 0.5 hours = 30 minutes
  });

  it('should handle hours, minutes and seconds', () => {
    expect(pipe.transform(2.5)).toBe('2h 30m'); // 2.5 hours = 2h 30m
  });

  it('should handle days', () => {
    expect(pipe.transform(25)).toBe('1d 1h'); // 25 hours = 1 day 1 hour
  });

  it('should handle years', () => {
    expect(pipe.transform(8760)).toBe('1y'); // 8760 hours = 1 year (365 days)
  });

  it('should handle complex time with years, days, hours, minutes, seconds', () => {
    // 1 year + 1 day + 1 hour + 1 minute + 1 second
    // = 365*24 + 24 + 1 + 1/60 + 1/3600 hours
    const complexHours = 365 * 24 + 24 + 1 + 1/60 + 1/3600;
    const result = pipe.transform(complexHours);
    expect(result).toBe('1y 1d 1h 1m 1s');
  });

  it('should handle large values', () => {
    expect(pipe.transform(17520)).toBe('2y'); // 2 years
  });

  it('should handle fractional values correctly', () => {
    expect(pipe.transform(1.25)).toBe('1h 15m'); // 1.25 hours = 1h 15m
  });
});
