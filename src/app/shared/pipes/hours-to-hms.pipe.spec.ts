import { HoursToHMSPipe } from './hours-to-hms.pipe';

describe('HoursToHMSPipe', () => {
  let pipe: HoursToHMSPipe;

  beforeEach(() => {
    pipe = new HoursToHMSPipe();
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return "N/A" for null input', () => {
    expect(pipe.transform(null)).toBe('N/A');
  });

  it('should return "N/A" for undefined input', () => {
    expect(pipe.transform(undefined)).toBe('N/A');
  });

  it('should return "0h" for 0 hours', () => {
    expect(pipe.transform(0)).toBe('0h');
  });

  it('should handle hours only', () => {
    expect(pipe.transform(5)).toBe('5 giờ');
  });

  it('should handle days and hours (2 units)', () => {
    expect(pipe.transform(25)).toBe('1 ngày 1 giờ'); // 25 hours = 1 day 1 hour
  });

  it('should handle weeks and days (2 units)', () => {
    expect(pipe.transform(200)).toBe('1 tuần 32 giờ'); // 200 hours = 1 week + remaining hours
  });

  it('should handle months and weeks (2 units)', () => {
    expect(pipe.transform(1000)).toBe('1 tháng 2 tuần'); // 1000 hours ≈ 1 month 2 weeks
  });

  it('should handle years and months (2 units)', () => {
    expect(pipe.transform(10000)).toBe('1 năm 1 tháng'); // 10000 hours ≈ 1 year 1 month
  });

  it('should only show 2 most significant units', () => {
    // Large value that would have years, months, weeks, days, hours
    // Should only show the 2 most significant (years and months)
    const largeHours = 365 * 24 + 30 * 24 + 7 * 24 + 24 + 5; // 1y 1m 1w 1d 5h
    const result = pipe.transform(largeHours);
    expect(result).toBe('1 năm 1 tháng'); // Only shows years and months
  });

  it('should handle fractional hours (rounds down)', () => {
    expect(pipe.transform(1.9)).toBe('1 giờ'); // 1.9 hours rounds down to 1 hour
  });

  it('should handle small values', () => {
    expect(pipe.transform(0.5)).toBe('0h'); // Less than 1 hour shows 0h
  });
});
