import { HoursToHMSPipe } from './hours-to-hms.pipe';

describe('HoursToHMSPipe', () => {
  let pipe: HoursToHMSPipe;

  beforeEach(() => {
    pipe = new HoursToHMSPipe();
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return "N/A" for null input', () => {
    expect(pipe.transform(null)).toBe('N/A');
  });

  it('should return "N/A" for undefined input', () => {
    expect(pipe.transform(undefined)).toBe('N/A');
  });

  it('should return "0s" for 0 hours', () => {
    expect(pipe.transform(0)).toBe('0s');
  });

  it('should handle seconds only', () => {
    expect(pipe.transform(0.001)).toBe('3s'); // 0.001 hours = 3.6 seconds ≈ 3s
  });

  it('should handle minutes and seconds', () => {
    expect(pipe.transform(0.5)).toBe('30m'); // 0.5 hours = 30 minutes
  });

  it('should handle hours and minutes (2 units)', () => {
    expect(pipe.transform(1.5)).toBe('1h 30m'); // 1.5 hours = 1h 30m
  });

  it('should handle days and hours (2 units)', () => {
    expect(pipe.transform(25)).toBe('1d 1h'); // 25 hours = 1 day 1 hour
  });

  it('should handle weeks and days (2 units)', () => {
    expect(pipe.transform(200)).toBe('1w 32h'); // 200 hours = 1 week + remaining hours
  });

  it('should handle months and weeks (2 units)', () => {
    expect(pipe.transform(1000)).toBe('1mo 2w'); // 1000 hours ≈ 1 month 2 weeks
  });

  it('should handle years and months (2 units)', () => {
    expect(pipe.transform(10000)).toBe('1y 1mo'); // 10000 hours ≈ 1 year 1 month
  });

  it('should only show 2 most significant units', () => {
    // Large value that would have years, months, weeks, days, hours, minutes, seconds
    // Should only show the 2 most significant (years and months)
    const largeHours = 365 * 24 + 30 * 24 + 7 * 24 + 24 + 5 + 1/60 + 1/3600; // 1y 1mo 1w 1d 5h 1m 1s
    const result = pipe.transform(largeHours);
    expect(result).toBe('1y 1mo'); // Only shows years and months
  });

  it('should handle fractional hours with minutes and seconds', () => {
    expect(pipe.transform(1.25)).toBe('1h 15m'); // 1.25 hours = 1h 15m
  });

  it('should handle small fractional values', () => {
    expect(pipe.transform(0.01)).toBe('36s'); // 0.01 hours = 36 seconds
  });

  it('should handle complex time with minutes and seconds', () => {
    expect(pipe.transform(0.0175)).toBe('1m 3s'); // 0.0175 hours = 1m 3s (63 seconds)
  });
});
