/* Custom styles for Material datepicker */

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Material Calendar Styles */
.mat-datepicker-container {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.2s ease-out;
}

.custom-calendar {
  width: 100%;
  max-width: 700px;
  margin: 0 auto;
  background-color: transparent;
  font-family: inherit;
}

/* Make the calendar display multiple months side by side */
::ng-deep .mat-calendar {
  width: 100%;
  font-family: inherit;
}

::ng-deep .mat-calendar-header {
  padding: 8px 8px 0px 8px;
  background-color: transparent;
}

::ng-deep .mat-calendar-content {
  padding: 0 8px 8px 8px;
}

::ng-deep .mat-calendar-table {
  width: 100%;
}

/* Style the calendar period buttons */
::ng-deep .mat-calendar-period-button {
  color: var(--color-light-primary);
}

::ng-deep .dark .mat-calendar-period-button {
  color: var(--color-dark-primary);
}

::ng-deep .mat-calendar-arrow {
  border-top-color: var(--color-light-primary);
}

::ng-deep .dark .mat-calendar-arrow {
  border-top-color: var(--color-dark-primary);
}

::ng-deep .mat-calendar-body-label {
  color: var(--color-light-text);
}

::ng-deep .dark .mat-calendar-body-label {
  color: var(--color-dark-text);
}

/* Style the calendar navigation buttons */
::ng-deep .mat-calendar-previous-button,
::ng-deep .mat-calendar-next-button {
  color: var(--color-light-primary);
}

::ng-deep .dark .mat-calendar-previous-button,
::ng-deep .dark .mat-calendar-next-button {
  color: var(--color-dark-primary);
}

/* Style the form fields */
::ng-deep .custom-mat-form-field .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

::ng-deep .custom-mat-form-field .mat-mdc-text-field-wrapper {
  background-color: transparent;
  padding: 0;
}

::ng-deep .custom-mat-form-field .mat-mdc-form-field-flex {
  height: 42px;
  align-items: center;
  padding: 0 8px;
}

/* Modern date field styling */
.modern-date-field {
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.modern-date-field:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .modern-date-field:hover {
  border-color: #4b5563;
}

/* Hide the default outline */
::ng-deep .modern-date-field .custom-mat-form-field .mdc-notched-outline__leading,
::ng-deep .modern-date-field .custom-mat-form-field .mdc-notched-outline__notch,
::ng-deep .modern-date-field .custom-mat-form-field .mdc-notched-outline__trailing {
  border-color: transparent !important;
  border-width: 0 !important;
}

/* Style the form field text */
::ng-deep .modern-date-field .custom-mat-form-field .mat-mdc-input-element {
  padding: 8px 4px !important;
  font-size: 0.875rem !important;
}

/* Style the datepicker toggle icon */
::ng-deep .modern-date-field .mat-datepicker-toggle {
  color: #9ca3af !important;
}

::ng-deep .modern-date-field .mat-datepicker-toggle:hover {
  color: var(--color-light-primary) !important;
}

.dark ::ng-deep .modern-date-field .mat-datepicker-toggle:hover {
  color: var(--color-dark-primary) !important;
}

/* Style the label */
::ng-deep .modern-date-field .mat-mdc-form-field-label {
  font-size: 0.875rem !important;
  margin-left: 4px !important;
  color: #6b7280 !important;
}

.dark ::ng-deep .modern-date-field .mat-mdc-form-field-label {
  color: #9ca3af !important;
}

/* Style the form field text */
::ng-deep .custom-mat-form-field .mat-mdc-input-element {
  color: var(--color-light-text);
}

::ng-deep .dark .custom-mat-form-field .mat-mdc-input-element {
  color: var(--color-dark-text);
}

::ng-deep .custom-mat-form-field .mat-mdc-form-field-label {
  color: var(--color-light-text-gray);
}

::ng-deep .dark .custom-mat-form-field .mat-mdc-form-field-label {
  color: var(--color-dark-text-gray);
}

::ng-deep .custom-mat-form-field .mat-mdc-form-field-hint {
  color: var(--color-light-text-gray);
  font-size: 0.75rem;
}

::ng-deep .dark .custom-mat-form-field .mat-mdc-form-field-hint {
  color: var(--color-dark-text-gray);
}

/* Style the calendar cells */
::ng-deep .mat-calendar-body-cell-content {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-light-text);
}

::ng-deep .dark .mat-calendar-body-cell-content {
  color: var(--color-dark-text);
}

::ng-deep .mat-calendar-body-selected {
  background-color: var(--color-light-primary) !important;
  color: white !important;
}

::ng-deep .dark .mat-calendar-body-selected {
  background-color: var(--color-dark-primary) !important;
  color: white !important;
}

::ng-deep .mat-calendar-body-today:not(.mat-calendar-body-selected) {
  border-color: var(--color-light-primary) !important;
}

::ng-deep .dark .mat-calendar-body-today:not(.mat-calendar-body-selected) {
  border-color: var(--color-dark-primary) !important;
}

::ng-deep .mat-calendar-body-disabled > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
  color: var(--color-light-text-gray) !important;
}

::ng-deep .dark .mat-calendar-body-disabled > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
  color: var(--color-dark-text-gray) !important;
}

/* Fix cell content in popup */
::ng-deep .mat-datepicker-content .mat-calendar-body-cell-content {
  background-color: transparent !important;
}

::ng-deep .mat-datepicker-content .mat-calendar-body-selected {
  background-color: var(--color-light-primary) !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar-body-selected {
  background-color: var(--color-dark-primary) !important;
}

/* Style for date range */
::ng-deep .date-in-range:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.1) !important;
}

::ng-deep .dark .date-in-range:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.2) !important;
}

/* Fix date range in popup */
::ng-deep .mat-datepicker-content .date-in-range:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.1) !important;
}

::ng-deep .dark .mat-datepicker-content .date-in-range:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.2) !important;
}

/* Style for Material datepicker toggle icon */
::ng-deep .mat-datepicker-toggle-active {
  color: var(--color-light-primary);
}

::ng-deep .dark .mat-datepicker-toggle-active {
  color: var(--color-dark-primary);
}

/* Style for Material datepicker popup */
::ng-deep .mat-datepicker-content {
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05) !important;
  overflow: hidden !important;
  border: 1px solid #e5e7eb !important;
}

::ng-deep .dark .mat-datepicker-content {
  background-color: #1f2937 !important;
  color: white !important;
  border: 1px solid #374151 !important;
}

/* Fix transparent background in popup calendar */
::ng-deep .mat-datepicker-content .mat-calendar {
  background-color: white !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar {
  background-color: #1f2937 !important;
}

/* Fix calendar header in popup */
::ng-deep .mat-datepicker-content .mat-calendar-header {
  background-color: white !important;
  padding: 8px 8px 0px 8px !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar-header {
  background-color: #1f2937 !important;
}

/* Fix calendar content in popup */
::ng-deep .mat-datepicker-content .mat-calendar-content {
  background-color: white !important;
  padding: 0 8px 8px 8px !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar-content {
  background-color: #1f2937 !important;
}

/* Fix calendar table in popup */
::ng-deep .mat-datepicker-content .mat-calendar-table {
  background-color: white !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar-table {
  background-color: #1f2937 !important;
}

/* Fix calendar body in popup */
::ng-deep .mat-datepicker-content .mat-calendar-body {
  background-color: white !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar-body {
  background-color: #1f2937 !important;
}

/* Fix calendar cells in popup */
::ng-deep .mat-datepicker-content .mat-calendar-body-cell {
  background-color: white !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar-body-cell {
  background-color: #1f2937 !important;
}

/* Style calendar cells */
::ng-deep .mat-calendar-body-cell-content {
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

::ng-deep .mat-calendar-body-cell:hover .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.08) !important;
}

::ng-deep .dark .mat-calendar-body-cell:hover .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.15) !important;
}

/* Make sure the popup has a solid background */
::ng-deep .mat-datepicker-popup {
  background-color: white !important;
}

::ng-deep .dark .mat-datepicker-popup {
  background-color: #1f2937 !important;
}

/* Style the datepicker header - more compact */
.datepicker-header {
  padding: 0.5rem !important;
  background-color: rgba(249, 250, 251, 0.8) !important;
  border-radius: 8px !important;
  margin-bottom: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.datepicker-header select {
  font-weight: 600 !important;
  padding: 4px 8px !important;
  border-radius: 6px !important;
  border: 1px solid #e5e7eb !important;
  background-color: white !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  appearance: none !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%237241FF'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E") !important;
  background-repeat: no-repeat !important;
  background-position: right 6px center !important;
  background-size: 12px !important;
  padding-right: 24px !important;
  font-size: 0.8rem !important;
}

.datepicker-header select:hover {
  border-color: var(--color-light-primary) !important;
  box-shadow: 0 1px 3px rgba(114, 65, 255, 0.1) !important;
}

.datepicker-header select:focus {
  outline: none !important;
  border-color: var(--color-light-primary) !important;
  box-shadow: 0 0 0 3px rgba(114, 65, 255, 0.15) !important;
}

/* Style the datepicker days - more compact */
.datepicker-cell,
[inline-datepicker] .datepicker-cell {
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  margin: 1px !important;
  cursor: pointer !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 1 !important;
  font-size: 0.8rem !important;
}

.datepicker-cell::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  border-radius: 50% !important;
  z-index: -1 !important;
  transition: all 0.3s ease !important;
  transform: scale(0.8) !important;
  opacity: 0 !important;
}

/* Style the selected date */
.datepicker-cell.selected,
[inline-datepicker] .datepicker-cell.selected {
  color: white !important;
  transform: scale(1) !important;
  font-weight: 600 !important;
  position: relative !important;
}

.datepicker-cell.selected::before,
[inline-datepicker] .datepicker-cell.selected::before {
  background-color: var(--color-light-primary) !important;
  transform: scale(1) !important;
  opacity: 1 !important;
  box-shadow: 0 4px 6px -1px rgba(114, 65, 255, 0.2) !important;
}

/* Style today's date */
.datepicker-cell.today,
[inline-datepicker] .datepicker-cell.today {
  font-weight: 600 !important;
  position: relative !important;
}

.datepicker-cell.today::after,
[inline-datepicker] .datepicker-cell.today::after {
  content: '' !important;
  position: absolute !important;
  bottom: 6px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 4px !important;
  height: 4px !important;
  border-radius: 50% !important;
  background-color: var(--color-light-primary) !important;
}

.datepicker-cell.today.selected::after,
[inline-datepicker] .datepicker-cell.today.selected::after {
  background-color: white !important;
}

/* Hover effect for date cells */
.datepicker-cell:hover:not(.selected):not(.disabled)::before,
[inline-datepicker] .datepicker-cell:hover:not(.selected):not(.disabled)::before {
  background-color: rgba(114, 65, 255, 0.1) !important;
  transform: scale(1) !important;
  opacity: 1 !important;
}

/* Style the datepicker buttons - more compact */
.datepicker-footer {
  padding: 0.5rem !important;
  display: flex !important;
  justify-content: space-between !important;
  margin-top: 8px !important;
  background-color: rgba(249, 250, 251, 0.8) !important;
  border-radius: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.datepicker-footer button {
  padding: 0.3rem 0.7rem !important;
  font-size: 0.75rem !important;
  border-radius: 6px !important;
  background-color: var(--color-light-primary) !important;
  color: white !important;
  border: none !important;
  cursor: pointer !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  position: relative !important;
  overflow: hidden !important;
}

.datepicker-footer button::before {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 0 !important;
  height: 0 !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-radius: 50% !important;
  transform: translate(-50%, -50%) !important;
  transition: width 0.6s ease, height 0.6s ease !important;
}

.datepicker-footer button:hover::before {
  width: 300% !important;
  height: 300% !important;
}

.datepicker-footer button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px -2px rgba(114, 65, 255, 0.3) !important;
}

.datepicker-footer button:active {
  transform: translateY(0) !important;
}

/* Style the view switch */
.datepicker-view-switch {
  font-weight: 600 !important;
  color: #374151 !important;
  position: relative !important;
  padding-bottom: 2px !important;
}

.datepicker-view-switch::after {
  content: '' !important;
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 0 !important;
  height: 2px !important;
  background-color: var(--color-light-primary) !important;
  transition: width 0.3s ease !important;
}

.datepicker-view-switch:hover::after {
  width: 100% !important;
}

/* Style the days of week - more compact */
.datepicker-grid .datepicker-cell.day-name {
  font-weight: 600 !important;
  color: #6b7280 !important;
  font-size: 0.7rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.03em !important;
}

/* Dark mode support */
.dark .datepicker-dropdown {
  background-color: #1f2937 !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3) !important;
}

/* Dark mode for multiple months */
.dark .datepicker-dropdown .datepicker-picker {
  background-color: #1f2937 !important;
}

.dark .datepicker-header,
.dark .datepicker-footer {
  background-color: rgba(17, 24, 39, 0.8) !important;
}

.dark .datepicker-header select {
  background-color: #374151 !important;
  border-color: #4b5563 !important;
  color: white !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23FFFFFF'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E") !important;
}

.dark .datepicker-cell {
  color: #e5e7eb !important;
}

.dark .datepicker-cell.selected::before {
  background-color: var(--color-dark-primary) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3) !important;
}

.dark .datepicker-cell.today::after {
  background-color: var(--color-dark-primary) !important;
}

.dark .datepicker-cell:hover:not(.selected):not(.disabled)::before {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.dark .datepicker-footer button {
  background-color: var(--color-dark-primary) !important;
}

.dark .datepicker-view-switch {
  color: #e5e7eb !important;
}

.dark .datepicker-view-switch::after {
  background-color: var(--color-dark-primary) !important;
}

.dark .datepicker-grid .datepicker-cell.day-name {
  color: #9ca3af !important;
}

/* Style the date range buttons */
.date-picker-container {
  overflow: hidden !important;
}

.date-range-button {
  transition: all 0.2s ease !important;
}

.date-range-button:hover {
  border-color: var(--color-light-primary) !important;
  color: var(--color-light-primary) !important;
}

.dark .date-range-button:hover {
  border-color: var(--color-dark-primary) !important;
  color: var(--color-dark-primary) !important;
}

/* Style the date inputs */
.date-input {
  transition: all 0.2s ease !important;
}

.date-input:focus {
  border-color: var(--color-light-primary) !important;
}

/* Active date range button */
.date-range-button-active {
  background-color: var(--color-light-primary) !important;
  color: white !important;
  border-color: var(--color-light-primary) !important;
}

.date-range-button-active:hover {
  color: white !important;
}

.date-range-button-active svg {
  color: white !important;
}

.dark .date-range-button-active {
  background-color: var(--color-dark-primary) !important;
  border-color: var(--color-dark-primary) !important;
}

.dashboard-card {
  border-radius: 0.75rem;
  border: 1px solid var(--color-light-border-line);
  padding: 1rem;
  background: var(--color-base-200);
}

.card-header {
  margin-bottom: 1rem;
  font-weight: 600;
}

.card-content {
  padding: 0.5rem 0;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.progress-container {
  width: 100%;
  height: 0.5rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.dark .progress-container {
  background-color: #374151;
}

.progress-bar {
  height: 100%;
  border-radius: 9999px;
  transition: width 0.3s ease;
}

/* Status indicator classes for percentage values */
.positive {
  color: #10b981 !important; /* Green color for positive values */
}

.negative {
  color: #ef4444 !important; /* Red color for negative values */
}

.neutral {
  color: var(--color-light-text) !important; /* Normal text color for neutral values */
}

.dark .neutral {
  color: var(--color-dark-text) !important; /* Dark mode normal text color */
}
