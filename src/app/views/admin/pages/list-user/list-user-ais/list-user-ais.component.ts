import { CdkConnectedOverlay, CdkOverlayOrigin } from '@angular/cdk/overlay';
import {
  DatePipe,
  NgStyle,
  NgSwitch,
  NgS<PERSON>Case,
  NgSwitchDefault,
} from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import {
  DIALOG_DATA,
  DxButton,
  DxDialog,
  DxDialogRef,
  DxFormField,
  DxInput,
  DxLabel,
} from '@dx-ui/ui';
import { NgIcon, provideIcons } from '@ng-icons/core';
import { heroXCircle, heroXMark, heroEye, heroPencil } from '@ng-icons/heroicons/outline';
import {
  DataTableComponent,
  IColumn,
  SvgIconComponent,
} from '@shared/components';
import { ClickOutsideDirective } from '@shared/directives';
import { IUserInfo } from '@shared/models';
import { SettingsService } from '@shared/services';
import { DashboardComponent } from '@views/dashboard/dashboard.component';
import { EditAiUserComponent } from '@views/admin/pages/list-user/edit-ai-user/edit-ai-user.component';

@Component({
  selector: 'app-list-user-ais',
  imports: [
    DataTableComponent,
    DatePipe,
    DxLabel,
    NgIcon,
    NgSwitchCase,
    FormsModule,
    NgStyle,
    NgSwitch,
    NgSwitchDefault,
    DxFormField,
    DxInput,
    DxButton,
    SvgIconComponent,
    CdkConnectedOverlay,
    CdkOverlayOrigin,
    ClickOutsideDirective,
  ],
  providers: [
    provideIcons({
      heroXCircle,
      heroXMark,
      heroEye,
      heroPencil,
    }),
  ],
  templateUrl: './list-user-ais.component.html',
  styleUrl: './list-user-ais.component.css',
  host: {
    class: 'h-full',
  },
})
export class ListUserAisComponent implements OnInit {
  isLoading = signal<boolean>(false);

  filterAI: { user_id: number; name: string } = { user_id: 0, name: '' };
  count: number = 0;
  listAI = signal<any[]>([]);
  columnsAI: IColumn[] = [
    {
      columnDef: 'index',
      headerName: 'No.',
      flex: 0.1,
      minWidth: '40px',
    },
    {
      columnDef: 'name',
      headerName: 'Name',
      flex: 0.4,
      minWidth: '40px',
    },
    {
      columnDef: 'created_at',
      headerName: 'Created at',
      flex: 0.3,
      minWidth: '40px',
    },
    {
      columnDef: 'message_numb',
      headerName: 'Message used',
      flex: 0.1,
      minWidth: '40px',
    },
    {
      columnDef: 'action_edit',
      headerName: 'Action',
      flex: 0.2,
      minWidth: '120px',
      alignHeader: 'center',
      align: 'center',
    },
  ];
  aiChangeLLMId: any;

  dialogRef = inject(DxDialogRef<ListUserAisComponent>);
  data: { listAI: any; user: IUserInfo } = inject(DIALOG_DATA);
  private dialog = inject(DxDialog);
  private settingsService: SettingsService = inject(SettingsService);

  ngOnInit(): void {
    if (this.data.user.id != null) {
      this.filterAI.user_id = this.data.user.id;
      // this.listAI.set(this.data.listAI);
    }
    this.filterAIList();
  }

  editAI(ai: any) {
    this.aiChangeLLMId = ai.id;
    this.dialog.open(EditAiUserComponent, {
      data: {
        aiId: ai.id,
      },
      width: '80vw',
      height: '80vh',
      minWidth: '340px',
    });
  }

  showDashboard(data: any) {
    this.dialog.open(DashboardComponent, {
      data: {
        aiId: data.id,
      },
      width: '100vw',
      minWidth: '90vw',
      maxHeight: '90vh',
      panelClass: 'dashboard-dialog-container',
      hasBackdrop: true,
    });
  }

  filterAIList() {
    this.isLoading.set(true);
    this.settingsService.getListAIOwn(this.filterAI).subscribe({
      next: (res) => {
        this.isLoading.set(false);
        this.listAI.set(res);
        this.count = res.total;
      },
      error: () => {},
    });
  }
}
